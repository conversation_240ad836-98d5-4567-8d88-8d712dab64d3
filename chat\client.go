package chat

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

const (
	baseURL = "https://kimi.moonshot.cn/api"
)

// Client 聊天客户端
type Client struct {
	httpClient *http.Client
	token      string // Bearer token
	deviceID   string // 设备ID
	sessionID  string // 会话ID
}

// ClientOption 定义客户端选项
type ClientOption struct {
	Token     string
	DeviceID  string
	SessionID string
}

// NewClient 创建新的客户端实例
func NewClient(opt *ClientOption) *Client {
	return &Client{
		httpClient: &http.Client{},
		token:      opt.Token,
		deviceID:   opt.DeviceID,
		sessionID:  opt.SessionID,
	}
}

// setCommonHeaders 设置通用请求头
func (c *Client) setCommonHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.token))
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Origin", "https://kimi.moonshot.cn")
	req.Header.Set("Referer", "https://kimi.moonshot.cn/")
	// req.Header.Set("x-msh-device-id", c.deviceID)
	// req.Header.Set("x-msh-session-id", c.sessionID)
	req.Header.Set("x-msh-platform", "web")
	req.Header.Set("r-timezone", "Asia/Shanghai")
}

// CreateSession 创建会话
func (c *Client) CreateSession(req *CreateSessionRequest) (*CreateSessionResponse, error) {
	url := fmt.Sprintf("%s/chat", baseURL)

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	httpReq, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	c.setCommonHeaders(httpReq)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status: %d", resp.StatusCode)
	}

	var result CreateSessionResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &result, nil
}

// Chat 发送聊天消息并获取流式响应
func (c *Client) Chat(sessionID string, req *ChatRequest) (*http.Response, error) {
	url := fmt.Sprintf("%s/chat/%s/completion/stream", baseURL, sessionID)

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	httpReq, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	c.setCommonHeaders(httpReq)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		return nil, fmt.Errorf("request failed with status: %d", resp.StatusCode)
	}

	return resp, nil
}
