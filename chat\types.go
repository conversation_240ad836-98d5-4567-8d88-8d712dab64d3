package chat

// CreateSessionRequest 创建会话请求
type CreateSessionRequest struct {
	Name       string `json:"name"`
	IsExample  bool   `json:"is_example"`
	KimiplusID string `json:"kimiplus_id"`
}

// CreateSessionResponse 创建会话响应
type CreateSessionResponse struct {
	ID              string      `json:"id"`
	Name            string      `json:"name"`
	ThumbStatus     ThumbStatus `json:"thumb_status"`
	CreatedAt       string      `json:"created_at"`
	IsExample       bool        `json:"is_example"`
	Status          string      `json:"status"`
	IsVoiceKimiplus int         `json:"is_voice_kimiplus"`
	Type            string      `json:"type"`
	Avatar          string      `json:"avatar"`
}

type ThumbStatus struct {
	IsThumbUp   bool `json:"is_thumb_up"`
	IsThumbDown bool `json:"is_thumb_down"`
}

// ChatRequest 聊天请求
type ChatRequest struct {
	KimiplusID        string    `json:"kimiplus_id"`
	Extend            Extend    `json:"extend"`
	Model             string    `json:"model"`
	UseSearch         bool      `json:"use_search"`
	Messages          []Message `json:"messages"`
	Refs              []string  `json:"refs"`
	History           []Message `json:"history"`
	SceneLabels       []string  `json:"scene_labels"`
	UseSemanticMemory bool      `json:"use_semantic_memory"`
	UseDeepResearch   bool      `json:"use_deep_research"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Extend struct {
	Sidebar bool `json:"sidebar"`
}

// EventType 定义事件类型
type EventType string

const (
	EventReq        EventType = "req"
	EventPing       EventType = "ping"
	EventResp       EventType = "resp"
	EventZoneSet    EventType = "zone_set"
	EventLoading    EventType = "loading"
	EventSearchPlus EventType = "search_plus"
	EventRename     EventType = "rename"
	EventCmpl       EventType = "cmpl"
	EventAllDone    EventType = "all_done"
)

// BaseEvent 基础事件结构
type BaseEvent struct {
	Event     EventType          `json:"event"`
	Content   string             `json:"content,omitempty"`
	GroupID   string             `json:"group_id,omitempty"`
	ID        string             `json:"id,omitempty"`
	Loading   bool               `json:"loading,omitempty"`
	Text      string             `json:"text,omitempty"`
	View      string             `json:"view,omitempty"`
	Refs      []interface{}      `json:"refs,omitempty"`
	SearchMsg *SearchPlusMessage `json:"msg,omitempty"`
}

// SearchPlusMessage 搜索相关消息
type SearchPlusMessage struct {
	Type       string   `json:"type"`
	Targets    []string `json:"targets,omitempty"`
	Date       string   `json:"date,omitempty"`
	SiteName   string   `json:"site_name,omitempty"`
	Snippet    string   `json:"snippet,omitempty"`
	SuccessNum int      `json:"successNum,omitempty"`
	Title      string   `json:"title,omitempty"`
	URL        string   `json:"url,omitempty"`
}
