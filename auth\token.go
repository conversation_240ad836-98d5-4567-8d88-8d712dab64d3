package auth

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/fatih/color"
)

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type SessionRequest struct {
	AppID        int    `json:"app_id"`
	UserUniqueID string `json:"user_unique_id"`
	WebID        string `json:"web_id"`
}

type SessionResponse struct {
	Error int    `json:"e"`
	ToBID string `json:"tobid"`
}

type WebIDRequest struct {
	AppID        int    `json:"app_id"`
	URL          string `json:"url"`
	UserAgent    string `json:"user_agent"`
	Referer      string `json:"referer"`
	UserUniqueID string `json:"user_unique_id"`
}

type WebIDResponse struct {
	Error int    `json:"e"`
	WebID string `json:"web_id"`
}

type ListEvent struct {
	Event        string `json:"event"`
	AbSdkVersion string `json:"ab_sdk_version"`
	Params       string `json:"params"`
	LocalTimeMs  int64  `json:"local_time_ms"`
}

type ListUser struct {
	UserUniqueID string `json:"user_unique_id"`
	WebID        string `json:"web_id"`
}

type ListHeader struct {
	AppID          int    `json:"app_id"`
	OsName         string `json:"os_name"`
	OsVersion      string `json:"os_version"`
	DeviceModel    string `json:"device_model"`
	Language       string `json:"language"`
	Platform       string `json:"platform"`
	SdkVersion     string `json:"sdk_version"`
	SdkLib         string `json:"sdk_lib"`
	Timezone       int    `json:"timezone"`
	TzOffset       int    `json:"tz_offset"`
	Resolution     string `json:"resolution"`
	Browser        string `json:"browser"`
	BrowserVersion string `json:"browser_version"`
	Referrer       string `json:"referrer"`
	ReferrerHost   string `json:"referrer_host"`
	Width          int    `json:"width"`
	Height         int    `json:"height"`
	ScreenWidth    int    `json:"screen_width"`
	ScreenHeight   int    `json:"screen_height"`
	Custom         string `json:"custom"`
}

type ListRequest struct {
	Events []ListEvent `json:"events"`
	User   ListUser    `json:"user"`
	Header ListHeader  `json:"header"`
}

type ListResponse struct {
	Type       string `json:"Type"`
	Error      int    `json:"e"`
	Message    string `json:"message"`
	Sc         int    `json:"sc"`
	ServerTime int64  `json:"server_time"`
	Tc         int    `json:"tc"`
}

const (
	defaultUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
	defaultAppID     = 20001731
)

// getSessionID 获取会话ID
func getSessionID(deviceID string) (string, error) {
	url := "https://gator.volces.com/tobid"
	color.Blue("\n>>> 正在请求会话ID...\n")

	reqBody := SessionRequest{
		AppID:        defaultAppID,
		UserUniqueID: deviceID,
		WebID:        deviceID,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}
	color.Yellow("-> 请求数据: %s\n", string(jsonData))

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", defaultUserAgent)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求session失败: %v", err)
	}
	defer resp.Body.Close()

	color.Yellow("-> 响应状态码: %d\n", resp.StatusCode)

	// 读取完整的响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}
	color.Yellow("-> 响应数据: %s\n", string(respBody))

	var sessionResp SessionResponse
	if err := json.Unmarshal(respBody, &sessionResp); err != nil {
		return "", fmt.Errorf("解析session响应失败: %v", err)
	}

	if sessionResp.Error != 0 {
		return "", fmt.Errorf("获取session失败，错误码: %d", sessionResp.Error)
	}

	color.Green("[+] 成功获取会话ID: %s\n", sessionResp.ToBID)
	return sessionResp.ToBID, nil
}

// getDeviceID 获取设备ID
func getDeviceID() (string, error) {
	url := "https://gator.volces.com/webid"

	reqBody := WebIDRequest{
		AppID:        defaultAppID,
		URL:          "https://kimi.moonshot.cn/",
		UserAgent:    defaultUserAgent,
		Referer:      "",
		UserUniqueID: "",
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", defaultUserAgent)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求deviceID失败: %v", err)
	}
	defer resp.Body.Close()

	var webIDResp WebIDResponse
	if err := json.NewDecoder(resp.Body).Decode(&webIDResp); err != nil {
		return "", fmt.Errorf("解析deviceID响应失败: %v", err)
	}

	if webIDResp.Error != 0 {
		return "", fmt.Errorf("获取deviceID失败，错误码: %d", webIDResp.Error)
	}

	return webIDResp.WebID, nil
}

// sendListRequest 发送 list 请求
func sendListRequest(deviceID string) error {
	url := "https://gator.volces.com/list"
	color.Blue("\n>>> 正在发送list请求...\n")

	currentTime := time.Now().UnixMilli()
	request := []ListRequest{
		{
			Events: []ListEvent{
				{
					Event:        "abtest_exposure",
					AbSdkVersion: "20170220",
					Params:       "{\"ab_url\":\"https://kimi.moonshot.cn/\"}",
					LocalTimeMs:  currentTime,
				},
			},
			User: ListUser{
				UserUniqueID: deviceID,
				WebID:        deviceID,
			},
			Header: ListHeader{
				AppID:          defaultAppID,
				OsName:         "windows",
				OsVersion:      "10",
				DeviceModel:    "Windows NT 10.0",
				Language:       "zh-CN",
				Platform:       "web",
				SdkVersion:     "5.1.12_tob",
				SdkLib:         "js",
				Timezone:       8,
				TzOffset:       -28800,
				Resolution:     "2560x1440",
				Browser:        "Chrome",
				BrowserVersion: "*********",
				Referrer:       "",
				ReferrerHost:   "",
				Width:          2560,
				Height:         1440,
				ScreenWidth:    2560,
				ScreenHeight:   1440,
				Custom:         "{}",
			},
		},
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("序列化list请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建list请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", defaultUserAgent)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送list请求失败: %v", err)
	}
	defer resp.Body.Close()

	var listResp ListResponse
	if err := json.NewDecoder(resp.Body).Decode(&listResp); err != nil {
		return fmt.Errorf("解析list响应失败: %v", err)
	}

	if listResp.Error != 0 {
		return fmt.Errorf("list请求失败，错误码: %d", listResp.Error)
	}

	color.Green("[+] list请求成功: %s\n", listResp.Message)
	return nil
}

// GetToken 从 Kimi API 获取访问令牌
func GetToken() (string, error) {
	deviceID, err := getDeviceID()
	if err != nil {
		color.Red("\n[x] 获取设备ID失败: %v\n", err)
		return "", err
	}
	color.Green("[+] 获取设备ID: %s\n", deviceID)

	if err := sendListRequest(deviceID); err != nil {
		color.Red("[x] 发送list请求失败: %v\n", err)
		return "", err
	}
	time.Sleep(1 * time.Second)

	sessionID, err := getSessionID(deviceID)
	if err != nil {
		color.Red("[x] 获取会话ID失败: %v\n", err)
		return "", err
	}
	color.Green("[+] 获取会话ID: %s\n", sessionID)

	url := "https://kimi.moonshot.cn/api/device/register"
	color.Blue("\n>>> 正在请求token...\n")

	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte("{}")))
	if err != nil {
		color.Red("[x] 创建请求失败: %v\n", err)
		return "", err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Origin", "https://kimi.moonshot.cn")
	req.Header.Set("Referer", "https://kimi.moonshot.cn/")
	req.Header.Set("User-Agent", defaultUserAgent)
	req.Header.Set("r-timezone", "Asia/Shanghai")
	req.Header.Set("x-msh-platform", "web")
	req.Header.Set("x-msh-device-id", deviceID)
	req.Header.Set("x-msh-session-id", sessionID)
	req.Header.Set("x-traffic-id", deviceID)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		color.Red("[x] 请求token失败: %v\n", err)
		return "", err
	}
	defer resp.Body.Close()

	color.Yellow("-> 收到响应，状态码: %d\n", resp.StatusCode)

	var tokenResp TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		color.Red("[x] 解析token响应失败: %v\n", err)
		return "", err
	}

	tokenPreview := tokenResp.AccessToken
	color.Green("\n[+] 成功获取token: %s\n", tokenPreview)

	return tokenResp.AccessToken, nil
}
