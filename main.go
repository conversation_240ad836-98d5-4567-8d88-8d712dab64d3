package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"kimiReverse/auth"
	"kimiReverse/chat"

	"github.com/fatih/color"
)

var searchCount int // 添加计数器变量

// handleEvent 处理不同类型的事件
func handleEvent(data string) {
	var baseEvent chat.BaseEvent
	if err := json.Unmarshal([]byte(data), &baseEvent); err != nil {
		return
	}

	switch baseEvent.Event {
	case chat.EventReq:
		return

	case chat.EventPing, chat.EventZoneSet, chat.EventRename:
		return

	case chat.EventResp:
		return

	case chat.EventLoading:
		if baseEvent.Loading {
			color.Cyan("\n⏳ 思考中...\n")
		}

	case chat.EventSearchPlus:
		if baseEvent.SearchMsg != nil {
			handleSearchPlusMessage(baseEvent.SearchMsg)
		}

	case chat.EventCmpl:
		if baseEvent.Text != "" {
			fmt.Print(baseEvent.Text)
		}

	case chat.EventAllDone:
		color.Green("\n\n✨ 本轮回答完成\n")
	}
}

// handleSearchPlusMessage 处理搜索相关消息
func handleSearchPlusMessage(msg *chat.SearchPlusMessage) {
	switch msg.Type {
	case "start":
		searchCount = 0 // 添加计数器初始化
		color.Blue("正在搜索相关信息...\n")

	case "target":
		return

	case "get_res":
		if msg.Snippet != "" {
			searchCount++ // 增加计数器
			dateStr := ""
			if msg.Date != "" {
				dateStr = fmt.Sprintf(" %s", msg.Date)
			}

			color.Magenta("%d. [%s](%s)  ", searchCount, msg.Title, msg.URL) // 添加序号
			// fmt.Printf("%s\n", msg.Snippet)

			footer := fmt.Sprintf("**来源：** %s", msg.SiteName)
			if dateStr != "" {
				footer += fmt.Sprintf(" | %s", dateStr)
			}
			color.Yellow(footer + "\n")
		}

	case "done":
		fmt.Print("\n")
	}
}

func main() {
	// 获取 token
	token, err := auth.GetToken()
	if err != nil {
		color.Red("\n❌ 获取token失败: %v\n", err)
		return
	}

	client := chat.NewClient(&chat.ClientOption{
		Token: token,
	})

	sessionReq := &chat.CreateSessionRequest{
		Name:       "未命名会话",
		IsExample:  false,
		KimiplusID: "kimi",
	}
	session, err := client.CreateSession(sessionReq)
	if err != nil {
		color.Red("\n❌ 创建会话失败: %v\n", err)
		return
	}
	color.Green("sessionID: %+v\n", session.ID)

	reader1 := bufio.NewReader(os.Stdin)

	for { // 添加无限循环
		color.Green("\n请输入你的问题：")
		question, _ := reader1.ReadString('\n')
		question = strings.TrimSpace(question)
		color.Blue("\n❓ 问题：%s\n", question)

		chatReq := &chat.ChatRequest{
			Messages: []chat.Message{
				{
					Role:    "user",
					Content: question,
				},
			},
			UseSearch:   true,
			Extend:      chat.Extend{Sidebar: true},
			KimiplusID:  "kimi",
			UseResearch: false,
			UseMath:     false,
			Refs:        []string{},
			RefsFile:    []string{},
		}

		resp, err := client.Chat(session.ID, chatReq)
		if err != nil {
			color.Red("\n❌ 发送消息失败: %v\n", err)
			return // 循环中发生错误时，暂时选择退出循环
		}
		defer resp.Body.Close()

		// 读取流式响应
		reader := bufio.NewReader(resp.Body)
		for {
			line, err := reader.ReadString('\n')
			if err != nil {
				if err.Error() == "EOF" {
					break
				}
				color.Red("读取响应失败: %v\n", err)
				return // 循环中发生错误时，暂时选择退出循环
			}

			line = strings.TrimSpace(line)
			if !strings.HasPrefix(line, "data: ") {
				continue
			}

			data := strings.TrimPrefix(line, "data: ")
			handleEvent(data)
		}
	}
}
